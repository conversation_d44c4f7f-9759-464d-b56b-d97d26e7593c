-- Complete fix for the product reviews system
-- This script will fix all database schema issues and ensure proper relationships

-- Step 1: Clean up existing reviews system
DROP VIEW IF EXISTS user_purchasable_reviews CASCADE;
DROP VIEW IF EXISTS product_ratings_summary CASCADE;
DROP FUNCTION IF EXISTS get_product_average_rating(UUID) CASCADE;
DROP FUNCTION IF EXISTS has_user_purchased_product(UUID, UUID) CASCADE;
DROP FUNCTION IF EXISTS update_product_rating() CASCADE;
DROP TRIGGER IF EXISTS update_product_rating_insert ON product_reviews;
DROP TRIGGER IF EXISTS update_product_rating_update ON product_reviews;
DROP TRIGGER IF EXISTS update_product_rating_delete ON product_reviews;

-- Step 2: Ensure products table has rating columns
DO $$
BEGIN
  -- Add rating column if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'products' AND column_name = 'rating'
  ) THEN
    ALTER TABLE products ADD COLUMN rating NUMERIC(3,1) DEFAULT 0;
  END IF;

  -- Add review_count column if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'products' AND column_name = 'review_count'
  ) THEN
    ALTER TABLE products ADD COLUMN review_count INTEGER DEFAULT 0;
  END IF;
END $$;

-- Step 3: Recreate product_reviews table with correct foreign key
DROP TABLE IF EXISTS product_reviews CASCADE;

CREATE TABLE product_reviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  comment TEXT,
  title TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 4: Create indexes for performance
CREATE INDEX idx_product_reviews_product_id ON product_reviews(product_id);
CREATE INDEX idx_product_reviews_user_id ON product_reviews(user_id);
CREATE INDEX idx_product_reviews_created_at ON product_reviews(created_at DESC);

-- Step 5: Enable RLS
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;

-- Step 6: Create RLS policies
-- Anyone can view reviews
CREATE POLICY "Anyone can view product reviews"
  ON product_reviews FOR SELECT
  USING (true);

-- Users can insert reviews for purchased products
CREATE POLICY "Users can insert reviews for purchased products"
  ON product_reviews FOR INSERT
  WITH CHECK (
    auth.uid()::text = (SELECT id::text FROM user_profiles WHERE auth_user_id = auth.uid()) AND
    user_id = (SELECT id FROM user_profiles WHERE auth_user_id = auth.uid()) AND
    EXISTS (
      SELECT 1 FROM orders o
      JOIN order_items oi ON o.id = oi.order_id
      WHERE o.user_id = (SELECT id FROM user_profiles WHERE auth_user_id = auth.uid())
      AND oi.product_id = product_reviews.product_id
      AND o.status IN ('delivered', 'shipped')
    )
  );

-- Users can update their own reviews
CREATE POLICY "Users can update their own reviews"
  ON product_reviews FOR UPDATE
  USING (user_id = (SELECT id FROM user_profiles WHERE auth_user_id = auth.uid()));

-- Users can delete their own reviews
CREATE POLICY "Users can delete their own reviews"
  ON product_reviews FOR DELETE
  USING (user_id = (SELECT id FROM user_profiles WHERE auth_user_id = auth.uid()));

-- Admins can manage all reviews
CREATE POLICY "Admins can manage all reviews"
  ON product_reviews FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE auth_user_id = auth.uid() AND role = 'admin'
    )
  );

-- Step 7: Create helper functions
-- Function to check if user has purchased a product
CREATE OR REPLACE FUNCTION has_user_purchased_product(input_user_id UUID, input_product_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  has_purchased BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM orders o
    JOIN order_items oi ON o.id = oi.order_id
    WHERE o.user_id = input_user_id
    AND oi.product_id = input_product_id
    AND o.status IN ('delivered', 'shipped')
  ) INTO has_purchased;

  RETURN has_purchased;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate product rating summary
CREATE OR REPLACE FUNCTION get_product_average_rating(input_product_id UUID)
RETURNS TABLE (average_rating NUMERIC, review_count BIGINT) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0) AS average_rating,
    COUNT(*) AS review_count
  FROM product_reviews
  WHERE product_id = input_product_id;
END;
$$ LANGUAGE plpgsql;

-- Step 8: Create views
-- Product ratings summary view
CREATE OR REPLACE VIEW product_ratings_summary AS
SELECT
  product_id,
  COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0) AS average_rating,
  COUNT(*) AS review_count
FROM product_reviews
GROUP BY product_id;

-- User purchasable reviews view
CREATE OR REPLACE VIEW user_purchasable_reviews AS
SELECT DISTINCT
  o.user_id,
  oi.product_id,
  p.name as product_name,
  o.id as order_id,
  o.created_at as purchase_date,
  CASE WHEN pr.id IS NULL THEN false ELSE true END as has_reviewed
FROM
  orders o
  JOIN order_items oi ON o.id = oi.order_id
  JOIN products p ON oi.product_id = p.id
  LEFT JOIN product_reviews pr ON pr.product_id = oi.product_id AND pr.user_id = o.user_id
WHERE
  o.status IN ('delivered', 'shipped');

-- Step 9: Create function to update product ratings
CREATE OR REPLACE FUNCTION update_product_rating()
RETURNS TRIGGER AS $$
DECLARE
  avg_rating NUMERIC;
  review_count BIGINT;
  target_product_id UUID;
BEGIN
  -- Get the product_id from the trigger
  target_product_id := COALESCE(NEW.product_id, OLD.product_id);

  -- Calculate new rating and count
  SELECT
    COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0),
    COUNT(*)
  INTO
    avg_rating,
    review_count
  FROM product_reviews
  WHERE product_id = target_product_id;

  -- Update the products table
  UPDATE products
  SET
    rating = avg_rating,
    review_count = review_count,
    updated_at = NOW()
  WHERE id = target_product_id;

  -- Notify about the change for real-time updates
  PERFORM pg_notify('product_rating_updated', json_build_object(
    'product_id', target_product_id,
    'rating', avg_rating,
    'review_count', review_count
  )::text);

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Step 10: Create triggers
CREATE TRIGGER update_product_rating_insert
AFTER INSERT ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

CREATE TRIGGER update_product_rating_update
AFTER UPDATE ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

CREATE TRIGGER update_product_rating_delete
AFTER DELETE ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

-- Step 11: Initialize existing product ratings
-- This will calculate ratings for products that already have reviews
DO $$
DECLARE
  product_record RECORD;
BEGIN
  FOR product_record IN
    SELECT DISTINCT product_id FROM product_reviews
  LOOP
    PERFORM update_product_rating() FROM product_reviews WHERE product_id = product_record.product_id LIMIT 1;
  END LOOP;
END $$;

-- Step 12: Add comments for documentation
COMMENT ON TABLE product_reviews IS 'Stores customer reviews and ratings for products';
COMMENT ON FUNCTION has_user_purchased_product IS 'Checks if a user has purchased a specific product';
COMMENT ON FUNCTION get_product_average_rating IS 'Calculates average rating and review count for a product';
COMMENT ON FUNCTION update_product_rating IS 'Updates product rating in products table when reviews change';
COMMENT ON VIEW product_ratings_summary IS 'Provides rating summary for all products';
COMMENT ON VIEW user_purchasable_reviews IS 'Shows products users can review based on purchases';
