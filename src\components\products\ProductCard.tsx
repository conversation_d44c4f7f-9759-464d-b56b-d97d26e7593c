
import React, { useState, useRef, useEffect, memo, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Eye, ShoppingCart, Share2, Star, Check, AlertCircle, ChevronLeft, ChevronRight, FileText } from "lucide-react";
import { cn } from "@/lib/utils";
import { useCart } from "@/context/SupabaseCartContext";
import { useIsMobile } from "@/hooks/use-responsive";
import { OptimizedImage } from "@/components/ui/optimized-image";
import { shareProduct } from "@/utils/shareUtils";
import { toast } from "@/components/ui/use-toast";
import { useVisibilityAwareInterval } from "@/hooks/use-optimized-render";
import { useProductRating } from "@/hooks/useProductRating";

export interface ProductType {
  id: string;
  name: string;
  category: string;
  price: number;
  image: string;
  images?: string[];
  isNew?: boolean;
  isSale?: boolean;
  isFeatured?: boolean;
  salePrice?: number;
  stockStatus?: "in_stock" | "out_of_stock" | "low_stock";
  rating?: number;
  reviewCount?: number;
  description?: string;
  specifications?: { [key: string]: string };
  customizationAvailable?: boolean;
}

interface ProductCardProps {
  product: ProductType;
  className?: string;
}

const ProductCard = ({ product, className }: ProductCardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showSpecifications, setShowSpecifications] = useState(false);
  const isMobile = useIsMobile();
  const imageRef = useRef<HTMLDivElement>(null);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const [showSwipeHint, setShowSwipeHint] = useState(false);
  const {
    id,
    name,
    category,
    price,
    image,
    images = [],
    isNew,
    isSale,
    salePrice,
    stockStatus = "in_stock",
    rating: fallbackRating = 0,
    reviewCount: fallbackReviewCount = 0,
    description,
    specifications = {},
    customizationAvailable = false
  } = product;

  // Get real-time rating data
  const { rating: realRating, reviewCount: realReviewCount, isLoading: ratingLoading } = useProductRating(id);

  // Use real rating data if available, otherwise fall back to product props
  const rating = realRating || fallbackRating;
  const reviewCount = realReviewCount || fallbackReviewCount;
  const { addToCart } = useCart();
  const navigate = useNavigate();

  // Memoize the images array to prevent unnecessary recalculations
  const allImages = useMemo(() => [image, ...images.filter(img => img !== image && img)], [image, images]);

  // Memoize event handlers to prevent unnecessary re-renders
  const handleAddToCart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(product, 1);
  }, [addToCart, product]);

  const handleBuyNow = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(product, 1);
    navigate('/cart');
  }, [addToCart, product, navigate]);

  const handleQuickView = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/products/${id}`);
  }, [navigate, id]);

  const handleViewSpecifications = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowSpecifications(true);
  }, []);

  const handleProductClick = useCallback(() => {
    if (!showSpecifications) {
      navigate(`/products/${id}`);
    }
  }, [navigate, id, showSpecifications]);

  const handleShareProduct = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const success = await shareProduct(product);

    if (success) {
      toast({
        title: "Shared successfully",
        description: navigator.share ? "Product shared successfully" : "Product link copied to clipboard",
      });
    } else {
      toast({
        title: "Sharing failed",
        description: "Unable to share this product. Please try again.",
        variant: "destructive",
      });
    }
  };



  const handleNextImage = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    setCurrentImageIndex((prev) =>
      prev === allImages.length - 1 ? 0 : prev + 1
    );
  };

  const handlePrevImage = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    setCurrentImageIndex((prev) =>
      prev === 0 ? allImages.length - 1 : prev - 1
    );
  };

  // Auto-change images every 4 seconds with tab visibility awareness
  useVisibilityAwareInterval(
    () => {
      setCurrentImageIndex((prev) => (prev === allImages.length - 1 ? 0 : prev + 1));
    },
    4000,
    allImages.length > 1 && !isHovered && !touchStart
  );

  // Show swipe hint for mobile users on first render
  useEffect(() => {
    if (isMobile && allImages.length > 1) {
      // Show swipe hint after a short delay
      const timer = setTimeout(() => {
        setShowSwipeHint(true);

        // Hide swipe hint after animation completes
        const hideTimer = setTimeout(() => {
          setShowSwipeHint(false);
        }, 2000); // Match animation duration

        return () => clearTimeout(hideTimer);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isMobile, allImages.length]);

  // Add passive touch event listeners for better performance on mobile
  useEffect(() => {
    if (isMobile && imageRef.current && allImages.length > 1) {
      const element = imageRef.current;

      // Function to handle touch events with passive option
      const handleTouchStartPassive = (e: TouchEvent) => {
        const touch = e.touches[0];
        setTouchStart(touch.clientX);
        setTouchEnd(null);
      };

      const handleTouchMovePassive = (e: TouchEvent) => {
        if (!touchStart) return;
        const touch = e.touches[0];
        setTouchEnd(touch.clientX);
      };

      const handleTouchEndPassive = () => {
        if (!touchStart || !touchEnd) return;
        const distance = touchStart - touchEnd;
        const isLeftSwipe = distance > 30;
        const isRightSwipe = distance < -30;

        if (isLeftSwipe && allImages.length > 1) {
          handleNextImage();
        } else if (isRightSwipe && allImages.length > 1) {
          handlePrevImage();
        }

        setTouchStart(null);
        setTouchEnd(null);
      };

      // Add passive event listeners
      element.addEventListener('touchstart', handleTouchStartPassive, { passive: true });
      element.addEventListener('touchmove', handleTouchMovePassive, { passive: true });
      element.addEventListener('touchend', handleTouchEndPassive, { passive: true });
      element.addEventListener('touchcancel', handleTouchEndPassive, { passive: true });

      return () => {
        // Clean up event listeners
        element.removeEventListener('touchstart', handleTouchStartPassive);
        element.removeEventListener('touchmove', handleTouchMovePassive);
        element.removeEventListener('touchend', handleTouchEndPassive);
        element.removeEventListener('touchcancel', handleTouchEndPassive);
      };
    }
  }, [isMobile, imageRef, touchStart, touchEnd, allImages.length]);

  // Note: Auto-change pause/resume is now handled by useVisibilityAwareInterval hook

  // Touch handlers are now implemented using passive event listeners in the useEffect hook

  const renderStockIndicator = () => {
    switch (stockStatus) {
      case "in_stock":
        return (
          <div className="flex items-center text-xs text-green-600 font-medium">
            <Check className="h-3 w-3 mr-1" />
            <span>In Stock</span>
          </div>
        );
      case "out_of_stock":
        return (
          <div className="flex items-center text-xs text-red-500 font-medium">
            <AlertCircle className="h-3 w-3 mr-1" />
            <span>Out of Stock</span>
          </div>
        );
      case "low_stock":
        return (
          <div className="flex items-center text-xs text-amber-500 font-medium">
            <AlertCircle className="h-3 w-3 mr-1" />
            <span>Low Stock</span>
          </div>
        );
      default:
        return null;
    }
  };

  const renderRating = () => {
    return (
      <div className="flex items-center">
        <div className="flex">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={cn(
                "h-3 w-3",
                i < Math.round(rating) ? "text-yellow-400 fill-yellow-400" : "text-badhees-200"
              )}
            />
          ))}
        </div>
        {reviewCount > 0 && (
          <span className="ml-1 text-xs text-badhees-500">({reviewCount})</span>
        )}
        {ratingLoading && (
          <span className="ml-1 text-xs text-badhees-400">Loading...</span>
        )}
      </div>
    );
  };

  return (
    <div
      className={cn("product-card h-full group bg-transparent", className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        if (!showSpecifications) {
          setShowSpecifications(false);
        }
      }}
    >
      <div
        className="relative overflow-hidden rounded-lg aspect-square cursor-pointer transition-shadow duration-300 hover:shadow-md"
        onClick={handleProductClick}
      >
        <div
          className="w-full h-full touch-manipulation relative"
          ref={imageRef}
        >
          {isMobile && allImages.length > 1 && (
            <div className="absolute top-2 right-2 bg-black/40 text-white text-xs px-2 py-1 rounded-full z-10 pointer-events-none">
              {currentImageIndex + 1}/{allImages.length}
            </div>
          )}

          {/* Share button - visible on hover */}
          <div className="absolute top-3 right-3 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              type="button"
              className="p-2 rounded-full bg-white bg-opacity-80 hover:bg-opacity-100 text-badhees-800 shadow-sm"
              onClick={handleShareProduct}
              aria-label="Share product"
            >
              <Share2 className="h-4 w-4" />
            </button>
          </div>

          {/* Customizable badge */}
          {customizationAvailable && (
            <div className="absolute top-3 right-12 z-10">
              <span className="text-xs bg-badhees-100 text-badhees-800 px-2 py-1 rounded-full">
                Customizable
              </span>
            </div>
          )}

          {/* Swipe indicator for mobile */}
          {isMobile && showSwipeHint && allImages.length > 1 && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-20">
              <div className="bg-black/50 text-white px-4 py-2 rounded-full flex items-center animate-swipe-indicator">
                <ChevronLeft className="h-4 w-4 mr-1" />
                <span className="text-sm">Swipe</span>
                <ChevronRight className="h-4 w-4 ml-1" />
              </div>
            </div>
          )}
          <div className="w-full h-full">
            <OptimizedImage
              src={allImages[currentImageIndex] || '/placeholder.svg'}
              alt={name}
              fallbackSrc="/placeholder.svg"
              aspectRatio="1/1"
              objectFit="cover"
              className="transition-transform duration-700 group-hover:scale-105"
              priority={currentImageIndex === 0}
              containerClassName="!aspect-square" /* Force square aspect ratio */
            />
          </div>
        </div>

        {allImages.length > 1 && (
          <div className="absolute bottom-3 left-0 right-0 flex justify-center z-10">
            <div className="product-indicators-container">
              {allImages.map((_, idx) => (
                <button
                  type="button"
                  key={idx}
                  className={`product-indicator image-indicator-btn ${
                    currentImageIndex === idx ? 'active bg-badhees-accent' : 'bg-white'
                  }`}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setCurrentImageIndex(idx);
                  }}
                  aria-label={`Go to image ${idx + 1}`}
                />
              ))}
            </div>
          </div>
        )}

        <div className="absolute top-3 left-3 flex flex-col gap-2">
          {isNew && (
            <span className="px-2 py-1 text-xs font-medium bg-badhees-accent text-white rounded">
              New
            </span>
          )}
          {isSale && (
            <span className="px-2 py-1 text-xs font-medium bg-red-500 text-white rounded">
              Sale
            </span>
          )}
        </div>

        {/* Mobile action buttons - always visible */}
        {isMobile && (
          <div className="absolute bottom-0 left-0 right-0 flex items-center gap-2 p-3 bg-transparent backdrop-blur-sm">
            {/* Buy Now button - 40% width */}
            <button
              type="button"
              className="flex-grow basis-[40%] py-2 px-3 rounded-md bg-badhees-accent text-white hover:bg-badhees-700 transition-colors shadow-sm touch-target text-sm font-medium"
              onClick={handleBuyNow}
            >
              Buy Now
            </button>

            {/* View Details button - 40% width */}
            <button
              type="button"
              className="flex-grow basis-[40%] py-2 px-3 rounded-md border border-badhees-200 bg-white text-badhees-800 hover:bg-badhees-50 transition-colors shadow-sm touch-target text-sm font-medium"
              onClick={handleQuickView}
            >
              View Details
            </button>

            {/* Add to Cart icon button - 20% width */}
            <button
              type="button"
              className={cn(
                "p-3 rounded-full transition-colors shadow-sm touch-target flex-shrink-0",
                stockStatus === "out_of_stock"
                  ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                  : "bg-badhees-accent text-white hover:bg-badhees-700"
              )}
              aria-label="Add to cart"
              onClick={stockStatus !== "out_of_stock" ? handleAddToCart : undefined}
              disabled={stockStatus === "out_of_stock"}
            >
              <ShoppingCart className="h-5 w-5" />
            </button>
          </div>
        )}

        {/* Desktop action buttons - visible on hover */}
        {!isMobile && (
          <div
            className={cn(
              "absolute bottom-0 left-0 right-0 flex items-center gap-2 p-4 bg-transparent backdrop-blur-sm transition-all duration-300",
              isHovered ? "opacity-100 translate-y-0" : "opacity-0 translate-y-full"
            )}
          >
            {/* Buy Now button - 40% width */}
            <button
              type="button"
              className="flex-grow basis-[40%] py-2 px-3 rounded-md bg-badhees-accent text-white hover:bg-badhees-700 transition-colors shadow-sm text-sm font-medium"
              onClick={handleBuyNow}
            >
              Buy Now
            </button>

            {/* View Details button - 40% width */}
            <button
              type="button"
              className="flex-grow basis-[40%] py-2 px-3 rounded-md border border-badhees-200 bg-white text-badhees-800 hover:bg-badhees-50 transition-colors shadow-sm text-sm font-medium"
              onClick={handleQuickView}
            >
              View Details
            </button>

            {/* Add to Cart icon button - 20% width */}
            <button
              type="button"
              className={cn(
                "p-2 rounded-full transition-colors shadow-sm flex-shrink-0",
                stockStatus === "out_of_stock"
                  ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                  : "bg-badhees-accent text-white hover:bg-badhees-700"
              )}
              aria-label="Add to cart"
              onClick={stockStatus !== "out_of_stock" ? handleAddToCart : undefined}
              disabled={stockStatus === "out_of_stock"}
            >
              <ShoppingCart className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>

      <div className="pt-4 pb-2 cursor-pointer bg-transparent" onClick={handleProductClick}>
        <div className="text-xs text-badhees-500 mb-1">{category}</div>
        <h3 className="font-medium text-badhees-800 hover:text-badhees-accent transition-colors line-clamp-2">
          {name}
        </h3>

        <div className="mt-1">
          {renderStockIndicator()}
        </div>

        <div className="mt-1">
          {renderRating()}
        </div>

        {description && (
          <p className="mt-2 text-xs text-badhees-600 line-clamp-2">
            {description}
          </p>
        )}

        <div className="mt-2 flex items-center">
          {isSale && salePrice ? (
            <>
              <span className="text-badhees-accent font-medium">₹{salePrice.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
              <span className="ml-2 text-badhees-400 text-sm line-through">₹{price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
            </>
          ) : (
            <span className="text-badhees-800 font-medium">₹{price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
          )}
        </div>
      </div>

      {showSpecifications && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
          onClick={(e) => {
            e.stopPropagation();
            setShowSpecifications(false);
          }}
        >
          <div
            className="bg-white rounded-lg p-6 max-w-md w-full max-h-[80vh] overflow-y-auto shadow-xl"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-semibold text-badhees-800">{name} Specifications</h3>
              <button
                type="button"
                className="p-1 hover:bg-badhees-100 rounded-full"
                onClick={() => setShowSpecifications(false)}
                aria-label="Close specifications"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5 text-badhees-500">
                  <path d="M18 6 6 18"></path>
                  <path d="m6 6 12 12"></path>
                </svg>
              </button>
            </div>

            {Object.keys(specifications).length > 0 ? (
              <div className="space-y-3">
                {Object.entries(specifications).map(([key, value]) => (
                  <div key={key} className="border-b border-badhees-100 pb-2">
                    <span className="text-sm font-medium text-badhees-700">{key}: </span>
                    <span className="text-sm text-badhees-600">{value}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-3 text-center text-badhees-500">
                <p>No specifications available for this product.</p>
                <p className="text-sm mt-2">Check the product details page for more information.</p>
              </div>
            )}

            <div className="mt-6 flex justify-end">
              <button
                type="button"
                className="px-4 py-2 bg-badhees-accent text-white rounded-md hover:bg-badhees-700 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowSpecifications(false);
                  navigate(`/products/${id}`);
                }}
              >
                View Product Details
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductCard;
