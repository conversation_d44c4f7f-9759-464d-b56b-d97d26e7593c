/**
 * Product Rating Hook with Real-time Updates
 *
 * This hook provides real-time product rating data with automatic updates
 * when reviews are added, updated, or deleted.
 */
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useRef, useMemo } from 'react';
import {
  getProductRatingSummary,
  subscribeToRatingUpdates,
  ProductRatingSummary
} from '@/services/productReviewsService';

// Query keys for product ratings
export const productRatingKeys = {
  all: ['product-ratings'] as const,
  rating: (productId: string) => [...productRatingKeys.all, 'rating', productId] as const,
  summary: (productId: string) => [...productRatingKeys.all, 'summary', productId] as const,
};

/**
 * Hook to get product rating with real-time updates
 * @param productId The product ID to get rating for
 * @returns Rating data with loading and error states
 */
export function useProductRating(productId: string) {
  const queryClient = useQueryClient();
  const subscriptionRef = useRef<(() => void) | null>(null);

  // Memoize the query key to prevent unnecessary re-renders
  const queryKey = useMemo(() => productRatingKeys.rating(productId), [productId]);

  // Query for product rating with optimized caching
  const query = useQuery({
    queryKey,
    queryFn: () => {
      console.log(`[ProductRating] Fetching rating for product: ${productId}`);
      return getProductRatingSummary(productId);
    },
    enabled: !!productId,
    // Use global settings for consistency
    // staleTime: 5 minutes (from global config)
    // gcTime: 15 minutes (from global config)
    // refetchOnWindowFocus: false (from global config)
    // refetchOnMount: false (from global config)
    // retry: 1 (from global config)
  });

  // Set up real-time subscription with ref to prevent re-subscriptions
  useEffect(() => {
    if (!productId) return;

    // Clean up existing subscription
    if (subscriptionRef.current) {
      subscriptionRef.current();
      subscriptionRef.current = null;
    }

    console.log(`[ProductRating] Setting up subscription for product ${productId}`);

    const unsubscribe = subscribeToRatingUpdates((updatedProductId, summary) => {
      if (updatedProductId === productId) {
        console.log(`[ProductRating] Received update for product ${productId}:`, summary);
        queryClient.setQueryData(queryKey, summary);
      }
    });

    subscriptionRef.current = unsubscribe;

    return () => {
      console.log(`[ProductRating] Cleaning up subscription for product ${productId}`);
      if (subscriptionRef.current) {
        subscriptionRef.current();
        subscriptionRef.current = null;
      }
    };
  }, [productId]); // Only depend on productId

  return {
    rating: query.data?.averageRating || 0,
    reviewCount: query.data?.reviewCount || 0,
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
  };
}

/**
 * Hook to get multiple product ratings efficiently
 * @param productIds Array of product IDs
 * @returns Map of product ratings
 */
export function useProductRatings(productIds: string[]) {
  const queryClient = useQueryClient();
  const subscriptionRef = useRef<(() => void) | null>(null);

  // Memoize the sorted product IDs to prevent unnecessary re-renders
  const sortedProductIds = useMemo(() => productIds.sort().join(','), [productIds]);
  const queryKey = useMemo(() => ['product-ratings-bulk', sortedProductIds], [sortedProductIds]);

  // Query for multiple product ratings
  const queries = useQuery({
    queryKey,
    queryFn: async () => {
      console.log(`[ProductRatings] Fetching ratings for ${productIds.length} products`);
      const ratings = new Map<string, ProductRatingSummary>();

      // Fetch ratings for all products
      await Promise.all(
        productIds.map(async (productId) => {
          try {
            const summary = await getProductRatingSummary(productId);
            ratings.set(productId, summary);
          } catch (error) {
            console.error(`Error fetching rating for product ${productId}:`, error);
            ratings.set(productId, { averageRating: 0, reviewCount: 0 });
          }
        })
      );

      return ratings;
    },
    enabled: productIds.length > 0,
    // Use global settings for consistency
    // staleTime: 5 minutes (from global config)
    // gcTime: 15 minutes (from global config)
    // refetchOnWindowFocus: false (from global config)
    // refetchOnMount: false (from global config)
    // retry: 1 (from global config)
  });

  // Set up real-time subscription for all products with ref to prevent re-subscriptions
  useEffect(() => {
    if (productIds.length === 0) return;

    // Clean up existing subscription
    if (subscriptionRef.current) {
      subscriptionRef.current();
      subscriptionRef.current = null;
    }

    console.log(`[ProductRatings] Setting up subscription for ${productIds.length} products`);

    const unsubscribe = subscribeToRatingUpdates((updatedProductId, summary) => {
      if (productIds.includes(updatedProductId)) {
        console.log(`[ProductRatings] Received update for product ${updatedProductId}:`, summary);

        // Update the bulk query cache
        queryClient.setQueryData(queryKey, (oldData: Map<string, ProductRatingSummary> | undefined) => {
          if (!oldData) return new Map([[updatedProductId, summary]]);
          const newData = new Map(oldData);
          newData.set(updatedProductId, summary);
          return newData;
        });

        // Also update individual product cache
        queryClient.setQueryData(productRatingKeys.rating(updatedProductId), summary);
      }
    });

    subscriptionRef.current = unsubscribe;

    return () => {
      console.log(`[ProductRatings] Cleaning up subscription`);
      if (subscriptionRef.current) {
        subscriptionRef.current();
        subscriptionRef.current = null;
      }
    };
  }, [sortedProductIds]); // Only depend on sortedProductIds

  return {
    ...queries,
    ratings: queries.data || new Map(),
    getRating: (productId: string) => queries.data?.get(productId) || { averageRating: 0, reviewCount: 0 },
  };
}

/**
 * Hook to prefetch product ratings for better performance
 * @param productIds Array of product IDs to prefetch
 */
export function usePrefetchProductRatings(productIds: string[]) {
  const queryClient = useQueryClient();

  // Memoize the product IDs to prevent unnecessary re-prefetching
  const memoizedProductIds = useMemo(() => productIds.sort().join(','), [productIds]);

  useEffect(() => {
    if (productIds.length === 0) return;

    console.log(`[ProductRatings] Prefetching ratings for ${productIds.length} products`);

    productIds.forEach((productId) => {
      queryClient.prefetchQuery({
        queryKey: productRatingKeys.rating(productId),
        queryFn: () => getProductRatingSummary(productId),
        // Use global staleTime setting for consistency
      });
    });
  }, [memoizedProductIds]); // Only depend on memoized product IDs
}
