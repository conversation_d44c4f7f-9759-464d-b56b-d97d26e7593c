/**
 * Product Rating Hook with Real-time Updates
 *
 * This hook provides real-time product rating data with automatic updates
 * when reviews are added, updated, or deleted.
 */
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState, useMemo } from 'react';
import {
  getProductRatingSummary,
  subscribeToRatingUpdates,
  invalidateProductRatingCache,
  ProductRatingSummary
} from '@/services/productReviewsService';

// Query keys for product ratings
export const productRatingKeys = {
  all: ['product-ratings'] as const,
  rating: (productId: string) => [...productRatingKeys.all, 'rating', productId] as const,
  summary: (productId: string) => [...productRatingKeys.all, 'summary', productId] as const,
};

/**
 * Hook to get product rating with real-time updates
 * @param productId The product ID to get rating for
 * @returns Rating data with loading and error states
 */
export function useProductRating(productId: string) {
  const queryClient = useQueryClient();

  // Query for product rating
  const query = useQuery({
    queryKey: productRatingKeys.rating(productId),
    queryFn: () => getProductRatingSummary(productId),
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    refetchOnMount: false, // Use cached data when available
  });

  // Set up real-time subscription - FIXED: Removed isSubscribed from dependencies
  useEffect(() => {
    if (!productId) return;

    console.log(`Setting up real-time rating subscription for product ${productId}`);

    const unsubscribe = subscribeToRatingUpdates((updatedProductId, summary) => {
      if (updatedProductId === productId) {
        console.log(`Received rating update for product ${productId}:`, summary);

        // Only update the specific query cache, don't invalidate
        queryClient.setQueryData(productRatingKeys.rating(productId), summary);
      }
    });

    return () => {
      console.log(`Cleaning up rating subscription for product ${productId}`);
      unsubscribe();
    };
  }, [productId, queryClient]); // FIXED: Removed isSubscribed from dependencies

  return {
    ...query,
    rating: query.data?.averageRating || 0,
    reviewCount: query.data?.reviewCount || 0,
  };
}

/**
 * Hook to get multiple product ratings efficiently
 * @param productIds Array of product IDs
 * @returns Map of product ratings
 */
export function useProductRatings(productIds: string[]) {
  const queryClient = useQueryClient();

  // Memoize the sorted product IDs to prevent unnecessary re-renders
  const sortedProductIds = useMemo(() => productIds.sort().join(','), [productIds]);

  // Query for multiple product ratings
  const queries = useQuery({
    queryKey: ['product-ratings-bulk', sortedProductIds],
    queryFn: async () => {
      const ratings = new Map<string, ProductRatingSummary>();

      // Fetch ratings for all products
      await Promise.all(
        productIds.map(async (productId) => {
          try {
            const summary = await getProductRatingSummary(productId);
            ratings.set(productId, summary);
          } catch (error) {
            console.error(`Error fetching rating for product ${productId}:`, error);
            ratings.set(productId, { averageRating: 0, reviewCount: 0 });
          }
        })
      );

      return ratings;
    },
    enabled: productIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    refetchOnMount: false, // Use cached data when available
  });

  // Set up real-time subscription for all products - FIXED: Removed isSubscribed
  useEffect(() => {
    if (productIds.length === 0) return;

    console.log(`Setting up bulk rating subscription for ${productIds.length} products`);

    const unsubscribe = subscribeToRatingUpdates((updatedProductId, summary) => {
      if (productIds.includes(updatedProductId)) {
        console.log(`Received bulk rating update for product ${updatedProductId}:`, summary);

        // Update the bulk query cache
        queryClient.setQueryData(
          ['product-ratings-bulk', sortedProductIds],
          (oldData: Map<string, ProductRatingSummary> | undefined) => {
            if (!oldData) return new Map([[updatedProductId, summary]]);

            const newData = new Map(oldData);
            newData.set(updatedProductId, summary);
            return newData;
          }
        );

        // Also update individual product cache
        queryClient.setQueryData(productRatingKeys.rating(updatedProductId), summary);
      }
    });

    return () => {
      console.log(`Cleaning up bulk rating subscription`);
      unsubscribe();
    };
  }, [productIds, queryClient, sortedProductIds]); // FIXED: Removed isSubscribed

  return {
    ...queries,
    ratings: queries.data || new Map(),
    getRating: (productId: string) => queries.data?.get(productId) || { averageRating: 0, reviewCount: 0 },
  };
}

/**
 * Hook to prefetch product ratings for better performance
 * @param productIds Array of product IDs to prefetch
 */
export function usePrefetchProductRatings(productIds: string[]) {
  const queryClient = useQueryClient();

  useEffect(() => {
    productIds.forEach((productId) => {
      queryClient.prefetchQuery({
        queryKey: productRatingKeys.rating(productId),
        queryFn: () => getProductRatingSummary(productId),
        staleTime: 5 * 60 * 1000, // 5 minutes
      });
    });
  }, [productIds, queryClient]);
}
